const path = require('path')
const files = require.context('./', false, /\.vue$/)

const modules = {}
const blockList = new Set(['砍价', '拼团', '秒杀', '积分商城', '签到', '小程序直播'])
files.keys().forEach(key => {
    const name = path.basename(key, '.vue')
    const mod = files(key).default || files(key)
    if (!mod.cname || blockList.has(mod.cname)) {
        return
    }
    modules[name] = mod
})
export default modules
